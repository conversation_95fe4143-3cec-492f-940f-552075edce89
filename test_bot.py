"""
اختبار بسيط للبوت
Simple Bot Test
"""

import sys
import os

print("🔍 اختبار البوت...")

try:
    # اختبار استيراد المكتبات
    print("📦 اختبار المكتبات...")
    
    import telegram
    print("✅ telegram - تم")
    
    import openai
    print("✅ openai - تم")
    
    from PIL import Image
    print("✅ PIL - تم")
    
    import requests
    print("✅ requests - تم")
    
    # اختبار استيراد الوحدات المحلية
    print("\n📁 اختبار الوحدات المحلية...")
    
    from config import TELEGRAM_BOT_TOKEN, OPENAI_API_KEY
    print("✅ config - تم")
    
    from image_analyzer import ImageAnalyzer
    print("✅ image_analyzer - تم")
    
    from trading_advisor import TradingAdvisor
    print("✅ trading_advisor - تم")
    
    from trading_knowledge import TradingKnowledge
    print("✅ trading_knowledge - تم")
    
    # اختبار المفاتيح
    print("\n🔑 اختبار المفاتيح...")
    
    if TELEGRAM_BOT_TOKEN and len(TELEGRAM_BOT_TOKEN) > 10:
        print("✅ Telegram Bot Token - موجود")
    else:
        print("❌ Telegram Bot Token - مفقود أو غير صحيح")
    
    if OPENAI_API_KEY and len(OPENAI_API_KEY) > 10:
        print("✅ OpenAI API Key - موجود")
    else:
        print("❌ OpenAI API Key - مفقود أو غير صحيح")
    
    # اختبار إنشاء الكائنات
    print("\n🏗️ اختبار إنشاء الكائنات...")
    
    analyzer = ImageAnalyzer()
    print("✅ ImageAnalyzer - تم إنشاؤه")
    
    advisor = TradingAdvisor()
    print("✅ TradingAdvisor - تم إنشاؤه")
    
    knowledge = TradingKnowledge()
    print("✅ TradingKnowledge - تم إنشاؤه")
    
    print("\n🎉 جميع الاختبارات نجحت!")
    print("✅ البوت جاهز للتشغيل")
    
except Exception as e:
    print(f"\n❌ خطأ في الاختبار: {e}")
    import traceback
    traceback.print_exc()
