"""
قاعدة المعرفة التداولية
Trading Knowledge Base
"""

class TradingKnowledge:
    def __init__(self):
        self.indicators = {
            "RSI": {
                "name": "مؤشر القوة النسبية",
                "description": "يقيس قوة حركة السعر ويحدد مناطق التشبع الشرائي والبيعي",
                "signals": {
                    "overbought": "فوق 70 - إشارة بيع محتملة",
                    "oversold": "تحت 30 - إشارة شراء محتملة",
                    "neutral": "بين 30-70 - منطقة محايدة"
                }
            },
            "MACD": {
                "name": "مؤشر تقارب وتباعد المتوسطات المتحركة",
                "description": "يظهر العلاقة بين متوسطين متحركين ويحدد اتجاه الزخم",
                "signals": {
                    "bullish_crossover": "عبور الخط فوق الإشارة - إشارة شراء",
                    "bearish_crossover": "عبور الخط تحت الإشارة - إشارة بيع",
                    "divergence": "التباعد مع السعر - إشارة انعكاس محتملة"
                }
            },
            "MA": {
                "name": "المتوسطات المتحركة",
                "description": "تنعم حركة السعر وتحدد الاتجاه العام",
                "signals": {
                    "golden_cross": "عبور MA قصير فوق MA طويل - إشارة صعود",
                    "death_cross": "عبور MA قصير تحت MA طويل - إشارة هبوط",
                    "support_resistance": "تعمل كمستويات دعم ومقاومة"
                }
            },
            "BOLLINGER_BANDS": {
                "name": "نطاقات بولينجر",
                "description": "تقيس التقلبات وتحدد مستويات الدعم والمقاومة الديناميكية",
                "signals": {
                    "squeeze": "ضيق النطاقات - توقع حركة قوية",
                    "expansion": "اتساع النطاقات - زيادة التقلبات",
                    "bounce": "ارتداد من الحدود - فرص تداول"
                }
            }
        }
        
        self.patterns = {
            "HAMMER": {
                "name": "المطرقة",
                "type": "انعكاس صعودي",
                "description": "شمعة بجسم صغير وذيل طويل في القاع",
                "reliability": "متوسطة إلى عالية"
            },
            "DOJI": {
                "name": "الدوجي",
                "type": "تردد",
                "description": "شمعة بجسم صغير جداً تدل على التردد",
                "reliability": "متوسطة"
            },
            "ENGULFING": {
                "name": "الابتلاع",
                "type": "انعكاس",
                "description": "شمعة تبتلع الشمعة السابقة بالكامل",
                "reliability": "عالية"
            },
            "HEAD_SHOULDERS": {
                "name": "الرأس والكتفين",
                "type": "انعكاس هبوطي",
                "description": "نمط يتكون من ثلاث قمم، الوسطى أعلى",
                "reliability": "عالية جداً"
            }
        }
        
        self.strategies = {
            "TREND_FOLLOWING": {
                "name": "متابعة الاتجاه",
                "description": "التداول في اتجاه الترند الرئيسي",
                "tools": ["Moving Averages", "MACD", "ADX"],
                "risk": "متوسطة",
                "timeframe": "متوسط إلى طويل الأجل"
            },
            "MEAN_REVERSION": {
                "name": "العودة للمتوسط",
                "description": "التداول على توقع عودة السعر للمتوسط",
                "tools": ["RSI", "Bollinger Bands", "Stochastic"],
                "risk": "متوسطة إلى عالية",
                "timeframe": "قصير إلى متوسط الأجل"
            },
            "BREAKOUT": {
                "name": "الاختراق",
                "description": "التداول على اختراق مستويات الدعم والمقاومة",
                "tools": ["Volume", "Support/Resistance", "Chart Patterns"],
                "risk": "عالية",
                "timeframe": "قصير الأجل"
            }
        }
    
    def get_indicator_info(self, indicator_name):
        """الحصول على معلومات مؤشر معين"""
        return self.indicators.get(indicator_name.upper(), "مؤشر غير موجود")
    
    def get_pattern_info(self, pattern_name):
        """الحصول على معلومات نمط معين"""
        return self.patterns.get(pattern_name.upper(), "نمط غير موجود")
    
    def get_strategy_info(self, strategy_name):
        """الحصول على معلومات استراتيجية معينة"""
        return self.strategies.get(strategy_name.upper(), "استراتيجية غير موجودة")
    
    def get_general_advice(self):
        """نصائح تداول عامة"""
        return """
💡 نصائح التداول الذهبية:

1. 📊 **إدارة المخاطر أولاً**
   - لا تخاطر بأكثر من 1-2% من رأس المال في صفقة واحدة
   - استخدم وقف الخسارة دائماً

2. 📈 **اتبع الخطة**
   - ضع خطة تداول واضحة قبل الدخول
   - لا تتداول بناءً على العواطف

3. 🎯 **نسبة المخاطرة للربح**
   - استهدف نسبة 1:2 على الأقل (مخاطرة 1 مقابل ربح 2)

4. 📚 **التعلم المستمر**
   - اقرأ وتعلم باستمرار
   - حلل صفقاتك السابقة

5. ⏰ **الصبر مفتاح النجاح**
   - انتظر الفرص الجيدة
   - لا تتداول من أجل التداول فقط

6. 💰 **إدارة رأس المال**
   - نوع محفظتك
   - لا تضع كل أموالك في استثمار واحد
"""
    
    def get_market_analysis_framework(self):
        """إطار عمل تحليل السوق"""
        return """
🔍 إطار تحليل السوق الشامل:

1. **التحليل الأساسي** 📊
   - الأخبار الاقتصادية
   - البيانات المالية للشركات
   - السياسات النقدية

2. **التحليل الفني** 📈
   - المؤشرات الفنية
   - أنماط الشموع
   - مستويات الدعم والمقاومة

3. **تحليل المشاعر** 🧠
   - مؤشر الخوف والطمع
   - حجم التداول
   - تحليل وسائل التواصل

4. **إدارة المخاطر** ⚠️
   - حجم المركز
   - وقف الخسارة
   - تنويع المحفظة
"""
