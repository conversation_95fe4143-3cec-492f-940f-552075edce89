"""
محلل الصور والشارتات
Image and Chart Analyzer using OpenAI Vision
"""

import base64
import io
from PIL import Image
import openai
from config import OPENAI_API_KEY
import logging

# إعداد OpenAI
openai.api_key = OPENAI_API_KEY

class ImageAnalyzer:
    def __init__(self):
        self.client = openai.OpenAI(api_key=OPENAI_API_KEY)
        
    def encode_image(self, image_bytes):
        """تحويل الصورة إلى base64"""
        return base64.b64encode(image_bytes).decode('utf-8')
    
    def resize_image(self, image_bytes, max_size=1024):
        """تصغير حجم الصورة إذا كانت كبيرة"""
        try:
            image = Image.open(io.BytesIO(image_bytes))
            
            # تصغير الصورة إذا كانت كبيرة
            if max(image.size) > max_size:
                ratio = max_size / max(image.size)
                new_size = tuple(int(dim * ratio) for dim in image.size)
                image = image.resize(new_size, Image.Resampling.LANCZOS)
            
            # تحويل إلى bytes
            output = io.BytesIO()
            image.save(output, format='PNG')
            return output.getvalue()
        except Exception as e:
            logging.error(f"خطأ في تصغير الصورة: {e}")
            return image_bytes
    
    async def analyze_chart(self, image_bytes):
        """تحليل الشارت باستخدام GPT-4 Vision"""
        try:
            # تصغير الصورة
            resized_image = self.resize_image(image_bytes)
            
            # تحويل إلى base64
            base64_image = self.encode_image(resized_image)
            
            # إعداد الرسالة للذكاء الاصطناعي
            messages = [
                {
                    "role": "system",
                    "content": """أنت خبير تداول محترف ومحلل فني متخصص. مهمتك تحليل الشارتات والرسوم البيانية وتقديم نصائح تداول دقيقة.

عند تحليل أي شارت، يجب أن تركز على:
1. اتجاه السعر العام (صاعد/هابط/جانبي)
2. المؤشرات الفنية الظاهرة (RSI, MACD, Moving Averages, إلخ)
3. مستويات الدعم والمقاومة
4. أنماط الشموع اليابانية
5. حجم التداول
6. نقاط الدخول والخروج المحتملة

قدم نصيحة واضحة: شراء/بيع/انتظار مع السبب."""
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "حلل هذا الشارت وأعطني نصيحة تداول مفصلة. أريد أن أعرف: 1) التحليل الفني 2) التوصية (شراء/بيع/انتظار) 3) نقاط الدخول والخروج 4) إدارة المخاطر"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ]
            
            # استدعاء GPT-4 Vision
            response = self.client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=messages,
                max_tokens=1000,
                temperature=0.3
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"خطأ في تحليل الصورة: {e}")
            return f"❌ عذراً، حدث خطأ في تحليل الصورة: {str(e)}"
    
    async def analyze_trading_setup(self, image_bytes):
        """تحليل إعداد التداول والإشارات"""
        try:
            resized_image = self.resize_image(image_bytes)
            base64_image = self.encode_image(resized_image)
            
            messages = [
                {
                    "role": "system",
                    "content": """أنت خبير في إشارات التداول وإعدادات الصفقات. ركز على:
1. تحديد نوع الإعداد (breakout, pullback, reversal, continuation)
2. قوة الإشارة (قوية/متوسطة/ضعيفة)
3. نسبة المخاطرة إلى الربح
4. التوقيت المناسب للدخول
5. مستويات وقف الخسارة وجني الأرباح"""
                },
                {
                    "role": "user",
                    "content": [
                        {
                            "type": "text",
                            "text": "حلل هذا الإعداد التداولي وأعطني تقييم شامل للفرصة"
                        },
                        {
                            "type": "image_url",
                            "image_url": {
                                "url": f"data:image/png;base64,{base64_image}"
                            }
                        }
                    ]
                }
            ]
            
            response = self.client.chat.completions.create(
                model="gpt-4-vision-preview",
                messages=messages,
                max_tokens=800,
                temperature=0.2
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"خطأ في تحليل الإعداد: {e}")
            return f"❌ عذراً، حدث خطأ في التحليل: {str(e)}"
