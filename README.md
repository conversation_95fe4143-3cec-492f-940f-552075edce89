# 🤖 TradAI Bot - بوت التداول الذكي

بوت تلغرام ذكي متخصص في تحليل الأسواق المالية وتقديم نصائح التداول باستخدام الذكاء الاصطناعي.

## ✨ المميزات

### 📊 تحليل الشارتات
- تحليل الصور والرسوم البيانية باستخدام GPT-4 Vision
- تحديد الاتجاهات والأنماط
- تقديم نصائح الدخول والخروج

### 💡 النصائح الذكية
- توصيات شراء/بيع/انتظار
- تحليل المؤشرات الفنية
- إدارة المخاطر

### 🧠 المعرفة الشاملة
- قاعدة بيانات شاملة عن التداول
- شرح المؤشرات والأنماط
- استراتيجيات التداول المختلفة

## 🚀 التثبيت والتشغيل

### المتطلبات
- Python 3.8+
- حساب Telegram Bot
- مفتاح OpenAI API

### خطوات التثبيت

1. **استنساخ المشروع:**
```bash
git clone <repository-url>
cd tradAi
```

2. **تثبيت المكتبات:**
```bash
pip install -r requirements.txt
```

3. **إعداد المفاتيح:**
- احصل على Bot Token من @BotFather في تلغرام
- احصل على OpenAI API Key
- أدخل المفاتيح في ملف `config.py`

4. **تشغيل البوت:**
```bash
python main.py
```

## 📱 كيفية الاستخدام

### الأوامر المتاحة
- `/start` - بدء المحادثة
- `/help` - عرض المساعدة
- `/analyze` - تحليل شارت
- `/advice` - نصائح تداول عامة
- `/indicators` - شرح المؤشرات الفنية
- `/market` - تحليل السوق اليومي

### تحليل الشارتات
1. أرسل صورة الشارت للبوت
2. انتظر التحليل الذكي
3. احصل على النصيحة والتوصية

### طرح الأسئلة
- اكتب أي سؤال عن التداول
- احصل على إجابة مفصلة من الخبير الذكي

## 🏗️ هيكل المشروع

```
tradAi/
├── main.py                 # الملف الرئيسي للبوت
├── config.py              # إعدادات البوت
├── image_analyzer.py      # محلل الصور
├── trading_advisor.py     # المستشار الذكي
├── trading_knowledge.py   # قاعدة المعرفة
├── requirements.txt       # المكتبات المطلوبة
├── database/
│   └── trading_data.json  # بيانات التداول
└── README.md             # هذا الملف
```

## 🔧 التخصيص

### إضافة مؤشرات جديدة
عدّل ملف `trading_knowledge.py` لإضافة مؤشرات فنية جديدة.

### تحسين التحليل
عدّل ملف `image_analyzer.py` لتحسين دقة تحليل الصور.

### إضافة ميزات جديدة
أضف معالجات جديدة في ملف `main.py`.

## 🛡️ الأمان

- لا تشارك مفاتيح API مع أحد
- استخدم متغيرات البيئة للمفاتيح الحساسة
- راجع الأذونات بانتظام

## ⚠️ تنبيه مهم

هذا البوت للأغراض التعليمية والمساعدة في التحليل فقط. 
**التداول ينطوي على مخاطر مالية عالية. استشر خبير مالي قبل اتخاذ أي قرارات استثمارية.**

## 🤝 المساهمة

نرحب بالمساهمات! يرجى:
1. عمل Fork للمشروع
2. إنشاء فرع جديد للميزة
3. إرسال Pull Request

## 📞 الدعم

للدعم والاستفسارات:
- أنشئ Issue في GitHub
- راسلنا عبر التلغرام

## 📄 الترخيص

هذا المشروع مرخص تحت رخصة MIT.

---

**صُنع بـ ❤️ للمجتمع العربي للتداول**
