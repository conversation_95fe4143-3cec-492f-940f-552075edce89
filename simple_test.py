"""
اختبار بسيط جداً
"""

print("🚀 بدء الاختبار...")

try:
    print("1. اختبار config...")
    from config import TELEGRAM_BOT_TOKEN
    print(f"   Token length: {len(TELEGRAM_BOT_TOKEN)}")
    
    print("2. اختبار telegram...")
    from telegram.ext import Application
    print("   ✅ telegram imported")
    
    print("3. إنشاء التطبيق...")
    app = Application.builder().token(TELEGRAM_BOT_TOKEN).build()
    print("   ✅ Application created")
    
    print("4. اختبار البوت...")
    from main import TradAIBot
    bot = TradAIBot()
    print("   ✅ Bot created")
    
    print("🎉 جميع الاختبارات نجحت!")
    
except Exception as e:
    print(f"❌ خطأ: {e}")
    import traceback
    traceback.print_exc()
