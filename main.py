"""
بوت التداول الذكي - TradAI Bot
Smart Trading Telegram Bot
"""

print("🚀 Starting bot loading...")

import logging
import asyncio
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, CallbackQueryHandler
from telegram.constants import ParseMode
import io

print("📦 Telegram libraries loaded...")

# Import local modules
from config import TELEGRAM_BOT_TOKEN, WELCOME_MESSAGE, HELP_MESSAGE, SUPPORTED_FORMATS, MAX_IMAGE_SIZE
print("⚙️ Configuration loaded...")

from image_analyzer import ImageAnalyzer
print("🖼️ Image analyzer loaded...")

from trading_advisor import TradingAdvisor
print("🧠 Trading advisor loaded...")

from trading_knowledge import TradingKnowledge
print("📚 Trading knowledge loaded...")

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TradAIBot:
    def __init__(self):
        self.image_analyzer = ImageAnalyzer()
        self.trading_advisor = TradingAdvisor()
        self.knowledge = TradingKnowledge()
        
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر البداية"""
        keyboard = [
            [InlineKeyboardButton("📊 Analyze Chart", callback_data='analyze')],
            [InlineKeyboardButton("💡 Trading Advice", callback_data='advice')],
            [InlineKeyboardButton("📈 Technical Indicators", callback_data='indicators')],
            [InlineKeyboardButton("🆘 Help", callback_data='help')]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            WELCOME_MESSAGE,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر المساعدة"""
        await update.message.reply_text(HELP_MESSAGE, parse_mode=ParseMode.MARKDOWN)
    
    async def handle_photo(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة الصور المرسلة"""
        try:
            # Send waiting message
            waiting_message = await update.message.reply_text("🔍 Analyzing chart... Please wait")

            # Get the image
            photo = update.message.photo[-1]  # Highest quality

            # Check image size
            if photo.file_size > MAX_IMAGE_SIZE:
                await waiting_message.edit_text("❌ Image size too large. Please send an image smaller than 10 MB.")
                return
            
            # Download the image
            file = await context.bot.get_file(photo.file_id)
            image_bytes = await file.download_as_bytearray()

            # Analyze the image
            analysis = await self.image_analyzer.analyze_chart(bytes(image_bytes))

            # Send the result
            await waiting_message.edit_text(f"📊 **Chart Analysis:**\n\n{analysis}", parse_mode=ParseMode.MARKDOWN)

            # Add buttons for more analysis
            keyboard = [
                [InlineKeyboardButton("🔍 Advanced Analysis", callback_data='advanced_analysis')],
                [InlineKeyboardButton("💡 Additional Tips", callback_data='more_advice')]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)

            await update.message.reply_text(
                "Would you like more analysis?",
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error(f"Error processing image: {e}")
            await update.message.reply_text("❌ Sorry, an error occurred while analyzing the image. Please try again.")
    
    async def handle_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة الرسائل النصية"""
        try:
            user_message = update.message.text
            
            # Send waiting message
            waiting_message = await update.message.reply_text("🤔 Thinking...")

            # Get advice from smart advisor
            advice = await self.trading_advisor.get_trading_advice(user_message)

            # Send response
            await waiting_message.edit_text(advice, parse_mode=ParseMode.MARKDOWN)

        except Exception as e:
            logger.error(f"Error processing text: {e}")
            await update.message.reply_text("❌ Sorry, an error occurred while processing your message. Please try again.")
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة الأزرار"""
        query = update.callback_query
        await query.answer()
        
        if query.data == 'analyze':
            await query.edit_message_text("📸 Send the chart image you want to analyze")

        elif query.data == 'advice':
            advice = self.knowledge.get_general_advice()
            await query.edit_message_text(advice, parse_mode=ParseMode.MARKDOWN)

        elif query.data == 'indicators':
            indicators_text = """
📊 **Available Technical Indicators:**

• RSI - Relative Strength Index
• MACD - Moving Average Convergence Divergence
• MA - Moving Averages
• BOLLINGER_BANDS - Bollinger Bands

Send indicator name for detailed explanation
Example: RSI
"""
            await query.edit_message_text(indicators_text, parse_mode=ParseMode.MARKDOWN)

        elif query.data == 'help':
            await query.edit_message_text(HELP_MESSAGE, parse_mode=ParseMode.MARKDOWN)

        elif query.data == 'advanced_analysis':
            await query.edit_message_text("🔍 Send another image for advanced analysis or ask a specific question")

        elif query.data == 'more_advice':
            framework = self.knowledge.get_market_analysis_framework()
            await query.edit_message_text(framework, parse_mode=ParseMode.MARKDOWN)
    
    async def analyze_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Analyze command"""
        await update.message.reply_text("📸 Send the chart image you want to analyze")

    async def advice_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Advice command"""
        advice = self.knowledge.get_general_advice()
        await update.message.reply_text(advice, parse_mode=ParseMode.MARKDOWN)

    async def indicators_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Indicators command"""
        indicators_text = """
📊 **Available Technical Indicators:**

• RSI - Relative Strength Index
• MACD - Moving Average Convergence Divergence
• MA - Moving Averages
• BOLLINGER_BANDS - Bollinger Bands

Send indicator name for detailed explanation
Example: RSI
"""
        await update.message.reply_text(indicators_text, parse_mode=ParseMode.MARKDOWN)

    async def market_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """Market command"""
        waiting_message = await update.message.reply_text("📊 Analyzing market...")

        analysis = await self.trading_advisor.generate_daily_analysis()
        await waiting_message.edit_text(analysis, parse_mode=ParseMode.MARKDOWN)

def main():
    """تشغيل البوت"""
    try:
        print("🚀 Starting TradAI Smart Trading Bot...")

        # Create application
        application = Application.builder().token(TELEGRAM_BOT_TOKEN).build()
        print("✅ Application created successfully")

        # Create bot
        bot = TradAIBot()
        print("✅ Bot created successfully")

        # Add handlers
        application.add_handler(CommandHandler("start", bot.start_command))
        application.add_handler(CommandHandler("help", bot.help_command))
        application.add_handler(CommandHandler("analyze", bot.analyze_command))
        application.add_handler(CommandHandler("advice", bot.advice_command))
        application.add_handler(CommandHandler("indicators", bot.indicators_command))
        application.add_handler(CommandHandler("market", bot.market_command))

        # Photo handler
        application.add_handler(MessageHandler(filters.PHOTO, bot.handle_photo))

        # Text handler
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, bot.handle_text))

        # Button handler
        application.add_handler(CallbackQueryHandler(bot.button_callback))

        print("✅ All handlers added successfully")

        # Run bot
        print("🤖 TradAI Smart Trading Bot is now running...")
        print("📱 You can now chat with the bot on Telegram!")
        application.run_polling(allowed_updates=Update.ALL_TYPES)

    except Exception as e:
        print(f"❌ Error starting bot: {e}")
        logger.error(f"Error starting bot: {e}")

if __name__ == '__main__':
    main()
