"""
بوت التداول الذكي - TradAI Bot
Smart Trading Telegram Bot
"""

print("🚀 بدء تحميل البوت...")

import logging
import asyncio
from telegram import Update, InlineKeyboardButton, InlineKeyboardMarkup
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes, CallbackQueryHandler
from telegram.constants import ParseMode
import io

print("📦 تم تحميل مكتبات التلغرام...")

# استيراد الوحدات المحلية
from config import TELEGRAM_BOT_TOKEN, WELCOME_MESSAGE, HELP_MESSAGE, SUPPORTED_FORMATS, MAX_IMAGE_SIZE
print("⚙️ تم تحميل الإعدادات...")

from image_analyzer import ImageAnalyzer
print("🖼️ تم تحميل محلل الصور...")

from trading_advisor import TradingAdvisor
print("🧠 تم تحميل المستشار الذكي...")

from trading_knowledge import TradingKnowledge
print("📚 تم تحميل قاعدة المعرفة...")

# إعداد التسجيل
logging.basicConfig(
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    level=logging.INFO
)
logger = logging.getLogger(__name__)

class TradAIBot:
    def __init__(self):
        self.image_analyzer = ImageAnalyzer()
        self.trading_advisor = TradingAdvisor()
        self.knowledge = TradingKnowledge()
        
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر البداية"""
        keyboard = [
            [InlineKeyboardButton("📊 تحليل شارت", callback_data='analyze')],
            [InlineKeyboardButton("💡 نصائح تداول", callback_data='advice')],
            [InlineKeyboardButton("📈 المؤشرات الفنية", callback_data='indicators')],
            [InlineKeyboardButton("🆘 المساعدة", callback_data='help')]
        ]
        reply_markup = InlineKeyboardMarkup(keyboard)
        
        await update.message.reply_text(
            WELCOME_MESSAGE,
            reply_markup=reply_markup,
            parse_mode=ParseMode.MARKDOWN
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر المساعدة"""
        await update.message.reply_text(HELP_MESSAGE, parse_mode=ParseMode.MARKDOWN)
    
    async def handle_photo(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة الصور المرسلة"""
        try:
            # إرسال رسالة انتظار
            waiting_message = await update.message.reply_text("🔍 جاري تحليل الشارت... يرجى الانتظار")
            
            # الحصول على الصورة
            photo = update.message.photo[-1]  # أعلى جودة
            
            # التحقق من حجم الصورة
            if photo.file_size > MAX_IMAGE_SIZE:
                await waiting_message.edit_text("❌ حجم الصورة كبير جداً. يرجى إرسال صورة أصغر من 10 ميجابايت.")
                return
            
            # تحميل الصورة
            file = await context.bot.get_file(photo.file_id)
            image_bytes = await file.download_as_bytearray()
            
            # تحليل الصورة
            analysis = await self.image_analyzer.analyze_chart(bytes(image_bytes))
            
            # إرسال النتيجة
            await waiting_message.edit_text(f"📊 **تحليل الشارت:**\n\n{analysis}", parse_mode=ParseMode.MARKDOWN)
            
            # إضافة أزرار للمزيد من التحليل
            keyboard = [
                [InlineKeyboardButton("🔍 تحليل متقدم", callback_data='advanced_analysis')],
                [InlineKeyboardButton("💡 نصائح إضافية", callback_data='more_advice')]
            ]
            reply_markup = InlineKeyboardMarkup(keyboard)
            
            await update.message.reply_text(
                "هل تريد المزيد من التحليل؟",
                reply_markup=reply_markup
            )
            
        except Exception as e:
            logger.error(f"خطأ في معالجة الصورة: {e}")
            await update.message.reply_text("❌ عذراً، حدث خطأ في تحليل الصورة. حاول مرة أخرى.")
    
    async def handle_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة الرسائل النصية"""
        try:
            user_message = update.message.text
            
            # إرسال رسالة انتظار
            waiting_message = await update.message.reply_text("🤔 جاري التفكير...")
            
            # الحصول على نصيحة من المستشار الذكي
            advice = await self.trading_advisor.get_trading_advice(user_message)
            
            # إرسال الرد
            await waiting_message.edit_text(advice, parse_mode=ParseMode.MARKDOWN)
            
        except Exception as e:
            logger.error(f"خطأ في معالجة النص: {e}")
            await update.message.reply_text("❌ عذراً، حدث خطأ في معالجة رسالتك. حاول مرة أخرى.")
    
    async def button_callback(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """معالجة الأزرار"""
        query = update.callback_query
        await query.answer()
        
        if query.data == 'analyze':
            await query.edit_message_text("📸 أرسل صورة الشارت الذي تريد تحليله")
            
        elif query.data == 'advice':
            advice = self.knowledge.get_general_advice()
            await query.edit_message_text(advice, parse_mode=ParseMode.MARKDOWN)
            
        elif query.data == 'indicators':
            indicators_text = """
📊 **المؤشرات الفنية المتاحة:**

• RSI - مؤشر القوة النسبية
• MACD - تقارب وتباعد المتوسطات
• MA - المتوسطات المتحركة  
• BOLLINGER_BANDS - نطاقات بولينجر

أرسل اسم المؤشر للحصول على شرح مفصل
مثال: RSI
"""
            await query.edit_message_text(indicators_text, parse_mode=ParseMode.MARKDOWN)
            
        elif query.data == 'help':
            await query.edit_message_text(HELP_MESSAGE, parse_mode=ParseMode.MARKDOWN)
            
        elif query.data == 'advanced_analysis':
            await query.edit_message_text("🔍 أرسل صورة أخرى للحصول على تحليل متقدم أو اسأل سؤال محدد")
            
        elif query.data == 'more_advice':
            framework = self.knowledge.get_market_analysis_framework()
            await query.edit_message_text(framework, parse_mode=ParseMode.MARKDOWN)
    
    async def analyze_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر التحليل"""
        await update.message.reply_text("📸 أرسل صورة الشارت الذي تريد تحليله")
    
    async def advice_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر النصائح"""
        advice = self.knowledge.get_general_advice()
        await update.message.reply_text(advice, parse_mode=ParseMode.MARKDOWN)
    
    async def indicators_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر المؤشرات"""
        indicators_text = """
📊 **المؤشرات الفنية المتاحة:**

• RSI - مؤشر القوة النسبية
• MACD - تقارب وتباعد المتوسطات
• MA - المتوسطات المتحركة  
• BOLLINGER_BANDS - نطاقات بولينجر

أرسل اسم المؤشر للحصول على شرح مفصل
مثال: RSI
"""
        await update.message.reply_text(indicators_text, parse_mode=ParseMode.MARKDOWN)
    
    async def market_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE):
        """أمر حالة السوق"""
        waiting_message = await update.message.reply_text("📊 جاري تحليل السوق...")
        
        analysis = await self.trading_advisor.generate_daily_analysis()
        await waiting_message.edit_text(analysis, parse_mode=ParseMode.MARKDOWN)

def main():
    """تشغيل البوت"""
    try:
        print("🚀 بدء تشغيل بوت التداول الذكي...")

        # إنشاء التطبيق
        application = Application.builder().token(TELEGRAM_BOT_TOKEN).build()
        print("✅ تم إنشاء التطبيق بنجاح")

        # إنشاء البوت
        bot = TradAIBot()
        print("✅ تم إنشاء البوت بنجاح")

        # إضافة المعالجات
        application.add_handler(CommandHandler("start", bot.start_command))
        application.add_handler(CommandHandler("help", bot.help_command))
        application.add_handler(CommandHandler("analyze", bot.analyze_command))
        application.add_handler(CommandHandler("advice", bot.advice_command))
        application.add_handler(CommandHandler("indicators", bot.indicators_command))
        application.add_handler(CommandHandler("market", bot.market_command))

        # معالج الصور
        application.add_handler(MessageHandler(filters.PHOTO, bot.handle_photo))

        # معالج النصوص
        application.add_handler(MessageHandler(filters.TEXT & ~filters.COMMAND, bot.handle_text))

        # معالج الأزرار
        application.add_handler(CallbackQueryHandler(bot.button_callback))

        print("✅ تم إضافة جميع المعالجات")

        # تشغيل البوت
        print("🤖 بوت التداول الذكي يعمل الآن...")
        print("📱 يمكنك الآن التحدث مع البوت في تلغرام!")
        application.run_polling(allowed_updates=Update.ALL_TYPES)

    except Exception as e:
        print(f"❌ خطأ في تشغيل البوت: {e}")
        logger.error(f"خطأ في تشغيل البوت: {e}")

if __name__ == '__main__':
    main()
