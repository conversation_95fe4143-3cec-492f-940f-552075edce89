"""
إعدادات بوت التداول الذكي
Trading Bot Configuration
"""

import os
from dotenv import load_dotenv

# تحميل المتغيرات من ملف .env
load_dotenv()

# إعدادات التلغرام
TELEGRAM_BOT_TOKEN = "**********************************************"

# إعدادات OpenAI
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

# إعدادات البوت
BOT_NAME = "TradAI Bot"
BOT_USERNAME = "@TradAI_Bot"

# Bot Messages
WELCOME_MESSAGE = """
🤖 Welcome to TradAI - Smart Trading Bot!

I'm an AI trading assistant specialized in financial market analysis. I can help you with:

📊 Chart and technical analysis
📈 Trading recommendations (buy/sell/hold)
💡 Answer your trading questions
🔍 Technical indicator analysis

📸 Send me a chart image and I'll analyze it for you!
❓ Or ask me any trading question

Use /help to see all available commands.
"""

HELP_MESSAGE = """
🆘 Available Bot Commands:

/start - Start conversation
/help - Show this message
/analyze - Analyze chart image
/advice - General trading advice
/indicators - Technical indicators explanation
/market - Market overview

📸 To analyze a chart:
1. Send chart image
2. Wait for AI analysis
3. Get trading recommendation

💬 You can also ask any question directly!
"""

# إعدادات التحليل
MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10 MB
SUPPORTED_FORMATS = ['jpg', 'jpeg', 'png', 'webp']

# إعدادات قاعدة البيانات
DATABASE_PATH = "database/"
TRADING_DATA_FILE = "database/trading_data.json"
