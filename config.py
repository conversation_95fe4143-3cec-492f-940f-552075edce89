"""
إعدادات بوت التداول الذكي
Trading Bot Configuration
"""

import os
from dotenv import load_dotenv

# تحميل المتغيرات من ملف .env
load_dotenv()

# إعدادات التلغرام
TELEGRAM_BOT_TOKEN = "**********************************************"

# إعدادات OpenAI
OPENAI_API_KEY = "********************************************************************************************************************************************************************"

# إعدادات البوت
BOT_NAME = "TradAI Bot"
BOT_USERNAME = "@TradAI_Bot"

# رسائل البوت
WELCOME_MESSAGE = """
🤖 مرحباً بك في بوت التداول الذكي TradAI!

أنا بوت ذكي متخصص في تحليل الأسواق المالية والتداول. يمكنني:

📊 تحليل الشارتات والرسوم البيانية
📈 تقديم نصائح التداول (شراء/بيع/انتظار)
💡 الإجابة على أسئلتك حول التداول
🔍 تحليل المؤشرات الفنية

📸 أرسل لي صورة الشارت وسأحللها لك!
❓ أو اسألني أي سؤال عن التداول

استخدم /help لمعرفة جميع الأوامر المتاحة.
"""

HELP_MESSAGE = """
🆘 أوامر البوت المتاحة:

/start - بدء المحادثة
/help - عرض هذه الرسالة
/analyze - تحليل صورة شارت
/advice - نصائح تداول عامة
/indicators - شرح المؤشرات الفنية
/market - حالة السوق العامة

📸 لتحليل شارت:
1. أرسل صورة الشارت
2. انتظر التحليل الذكي
3. احصل على النصيحة

💬 يمكنك أيضاً طرح أي سؤال مباشرة!
"""

# إعدادات التحليل
MAX_IMAGE_SIZE = 10 * 1024 * 1024  # 10 MB
SUPPORTED_FORMATS = ['jpg', 'jpeg', 'png', 'webp']

# إعدادات قاعدة البيانات
DATABASE_PATH = "database/"
TRADING_DATA_FILE = "database/trading_data.json"
