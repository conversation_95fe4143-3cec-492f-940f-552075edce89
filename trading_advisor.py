"""
مستشار التداول الذكي
Smart Trading Advisor
"""

import openai
from config import OPENAI_API_KEY
from trading_knowledge import TradingKnowledge
import logging

class TradingAdvisor:
    def __init__(self):
        self.client = openai.OpenAI(api_key=OPENAI_API_KEY)
        self.knowledge = TradingKnowledge()
        
    async def get_trading_advice(self, question):
        """Provide smart trading advice"""
        try:
            system_prompt = """You are a professional trading expert and specialized financial advisor. You have deep knowledge in:

1. Technical and fundamental analysis
2. Risk management
3. Various trading strategies
4. Forex, stocks, and cryptocurrency markets
5. Trading psychology

Provide practical and useful advice in English. Be accurate and detailed in your answers.
Always emphasize the importance of risk management."""

            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": question}
            ]
            
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=800,
                temperature=0.3
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"Error providing advice: {e}")
            return "❌ Sorry, an error occurred while providing advice. Please try again."
    
    async def analyze_market_sentiment(self, market_data=None):
        """تحليل مشاعر السوق"""
        try:
            prompt = """حلل الوضع العام للأسواق المالية حالياً وقدم تقييم لمشاعر السوق.
            ركز على:
            1. الاتجاه العام للأسواق
            2. مستوى الخوف والطمع
            3. التوصيات العامة للمتداولين
            4. القطاعات الواعدة"""
            
            messages = [
                {"role": "system", "content": "أنت محلل أسواق مالية خبير"},
                {"role": "user", "content": prompt}
            ]
            
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=600,
                temperature=0.4
            )
            
            return response.choices[0].message.content
            
        except Exception as e:
            logging.error(f"خطأ في تحليل المشاعر: {e}")
            return "❌ عذراً، حدث خطأ في تحليل السوق."
    
    def get_risk_management_advice(self, capital, risk_percentage=2):
        """نصائح إدارة المخاطر"""
        max_risk = capital * (risk_percentage / 100)
        
        advice = f"""
💰 **نصائح إدارة المخاطر لرأس مال {capital:,.0f}**

🎯 **المخاطرة المقترحة:** {risk_percentage}% = {max_risk:,.0f} لكل صفقة

📊 **قواعد إدارة المخاطر:**

1. **حجم المركز:**
   - لا تخاطر بأكثر من {max_risk:,.0f} في صفقة واحدة
   - احسب حجم المركز بناءً على وقف الخسارة

2. **التنويع:**
   - لا تضع أكثر من 10% في قطاع واحد
   - نوع بين الأسواق المختلفة

3. **نسبة المخاطرة للربح:**
   - استهدف 1:2 على الأقل
   - إذا كانت مخاطرتك {max_risk:,.0f}، استهدف ربح {max_risk*2:,.0f}

4. **وقف الخسارة:**
   - ضع وقف الخسارة دائماً قبل الدخول
   - لا تحرك وقف الخسارة ضدك

5. **إدارة العواطف:**
   - لا تتداول بناءً على الانتقام
   - خذ استراحة بعد سلسلة خسائر
"""
        return advice
    
    def get_indicator_explanation(self, indicator_name):
        """شرح المؤشرات الفنية"""
        indicator_info = self.knowledge.get_indicator_info(indicator_name)
        
        if isinstance(indicator_info, dict):
            explanation = f"""
📊 **{indicator_info['name']} ({indicator_name})**

📝 **الوصف:**
{indicator_info['description']}

🎯 **الإشارات:**
"""
            for signal, description in indicator_info['signals'].items():
                explanation += f"• {description}\n"
                
            return explanation
        else:
            return f"❌ المؤشر '{indicator_name}' غير موجود في قاعدة البيانات."
    
    def get_pattern_explanation(self, pattern_name):
        """شرح أنماط الشموع"""
        pattern_info = self.knowledge.get_pattern_info(pattern_name)
        
        if isinstance(pattern_info, dict):
            explanation = f"""
🕯️ **نمط {pattern_info['name']}**

📊 **النوع:** {pattern_info['type']}
📝 **الوصف:** {pattern_info['description']}
🎯 **الموثوقية:** {pattern_info['reliability']}

💡 **نصائح التداول:**
- انتظر تأكيد النمط بشمعة أخرى
- استخدم مؤشرات أخرى للتأكيد
- ضع وقف الخسارة بعناية
"""
            return explanation
        else:
            return f"❌ النمط '{pattern_name}' غير موجود في قاعدة البيانات."
    
    async def generate_daily_analysis(self):
        """تحليل يومي للأسواق"""
        try:
            prompt = """قدم تحليل يومي شامل للأسواق المالية يتضمن:
            1. نظرة عامة على الأسواق الرئيسية
            2. الأحداث المهمة لليوم
            3. الفرص التداولية المحتملة
            4. التحذيرات والمخاطر
            5. نصائح عملية للمتداولين"""
            
            messages = [
                {"role": "system", "content": "أنت محلل أسواق مالية يقدم تحليل يومي شامل"},
                {"role": "user", "content": prompt}
            ]
            
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=messages,
                max_tokens=1000,
                temperature=0.3
            )
            
            return f"📅 **التحليل اليومي للأسواق**\n\n{response.choices[0].message.content}"
            
        except Exception as e:
            logging.error(f"خطأ في التحليل اليومي: {e}")
            return "❌ عذراً، حدث خطأ في إنشاء التحليل اليومي."
