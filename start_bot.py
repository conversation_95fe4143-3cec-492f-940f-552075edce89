#!/usr/bin/env python3
"""
🤖 بوت التداول الذكي - TradAI Bot
تشغيل البوت النهائي
"""

import os
import sys
import signal
import time

def kill_existing_processes():
    """إيقاف العمليات الموجودة"""
    try:
        if os.name == 'nt':  # Windows
            os.system('taskkill /f /im python.exe 2>nul')
        else:  # Linux/Mac
            os.system('pkill -f "python.*main.py" 2>/dev/null')
        time.sleep(2)
    except:
        pass

def main():
    print("🚀 بدء تشغيل بوت التداول الذكي - TradAI")
    print("=" * 60)
    
    # إيقاف العمليات الموجودة
    print("🛑 إيقاف العمليات الموجودة...")
    kill_existing_processes()
    
    # تشغيل البوت
    print("🤖 تشغيل البوت...")
    try:
        from main import main as run_bot
        run_bot()
    except KeyboardInterrupt:
        print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
    except Exception as e:
        print(f"\n❌ خطأ في تشغيل البوت: {e}")

if __name__ == "__main__":
    main()
