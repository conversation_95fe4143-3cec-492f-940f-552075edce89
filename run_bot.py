"""
تشغيل البوت
Run Bot
"""

import sys
import os

print("🚀 بدء تشغيل بوت التداول الذكي...")
print("=" * 50)

try:
    # إضافة المجلد الحالي إلى المسار
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    print("📁 تم إعداد المسار...")
    
    # استيراد الوحدات
    print("📦 تحميل الوحدات...")
    from main import main
    
    print("✅ تم تحميل جميع الوحدات بنجاح!")
    print("🤖 بدء تشغيل البوت...")
    print("=" * 50)
    
    # تشغيل البوت
    main()
    
except KeyboardInterrupt:
    print("\n⏹️ تم إيقاف البوت بواسطة المستخدم")
except Exception as e:
    print(f"\n❌ خطأ في تشغيل البوت: {e}")
    import traceback
    traceback.print_exc()
